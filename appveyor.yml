version: "{build}-{branch}"
branches:
  except:
    - gh-pages
build: false
deploy: false
clone_depth: 2
image: Visual Studio 2022
environment:
  matrix:
    - PowerShell: 5
    - PowerShell: 7
matrix:
  fast_finish: true
for:
  - matrix:
      only:
        - PowerShell: 5
    cache:
      - '%USERPROFILE%\Documents\WindowsPowerShell\Modules -> appveyor.yml, test\bin\*.ps1'
      - C:\projects\helpers  -> appveyor.yml, test\bin\*.ps1
    install:
      - ps: .\test\bin\init.ps1
    test_script:
      - ps: .\test\bin\test.ps1
  - matrix:
      only:
        - PowerShell: 7
    cache:
      - '%USERPROFILE%\Documents\PowerShell\Modules -> appveyor.yml, test\bin\*.ps1'
      - C:\projects\helpers  -> appveyor.yml, test\bin\*.ps1
    install:
      - pwsh: .\test\bin\init.ps1
    test_script:
      - pwsh: .\test\bin\test.ps1
