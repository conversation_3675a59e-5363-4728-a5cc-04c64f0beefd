# Usage: scoop list [query]
# Summary: List installed apps
# Help: Lists all installed apps, or the apps matching the supplied query.
param($query)

. "$PSScriptRoot\..\lib\versions.ps1" # 'Select-CurrentVersion'
. "$PSScriptRoot\..\lib\manifest.ps1" # 'parse_json' 'Select-CurrentVersion' (indirectly)
. "$PSScriptRoot\..\lib\download.ps1" # 'Get-UserAgent'

$defaultArchitecture = Get-DefaultArchitecture
if (-not (Get-FormatData ScoopApps)) {
    Update-FormatData "$PSScriptRoot\..\supporting\formats\ScoopTypes.Format.ps1xml"
}

$local = installed_apps $false | ForEach-Object { @{ name = $_ } }
$global = installed_apps $true | ForEach-Object { @{ name = $_; global = $true } }

$apps = @($local) + @($global)
if (-not $apps) {
    Write-Host "There aren't any apps installed."
    exit 1
}

$list = @()
Write-Host "Installed apps$(if($query) { `" matching '$query'`"}):"
$apps | Where-Object { !$query -or ($_.name -match $query) } | ForEach-Object {
    $app = $_.name
    $global = $_.global
    $item = @{}
    $ver = Select-CurrentVersion -AppName $app -Global:$global
    $item.Name = $app
    $item.Version = $ver

    $install_info_path = "$(versiondir $app $ver $global)\install.json"
    $updated = (Get-Item (appdir $app $global)).LastWriteTime
    $install_info = $null
    if (Test-Path $install_info_path) {
        $install_info = parse_json $install_info_path
        $updated = (Get-Item $install_info_path).LastWriteTime
    }

    $item.Source = if ($install_info.bucket) {
        $install_info.bucket
    } elseif ($install_info.url) {
        if ($install_info.url -eq (usermanifest $app)) { '<auto-generated>' }
        else { $install_info.url }
    }
    $item.Updated = $updated

    $info = @()
    if ((app_status $app $global).deprecated) { $info += 'Deprecated package'}
    if ($global) { $info += 'Global install' }
    if (failed $app $global) { $info += 'Install failed' }
    if ($install_info.hold) { $info += 'Held package' }
    if ($install_info.architecture -and $defaultArchitecture -ne $install_info.architecture) {
        $info += $install_info.architecture
    }
    $item.Info = $info -join ', '

    $list += [PSCustomObject]$item
}

$list | Add-Member -TypeName 'ScoopApps' -PassThru
exit 0
