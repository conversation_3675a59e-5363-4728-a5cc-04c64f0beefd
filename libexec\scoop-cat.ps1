# Usage: scoop cat <app>
# Summary: Show content of specified manifest.
# Help: Show content of specified manifest.
# If configured, `bat` will be used to pretty-print the JSON.
# See `cat_style` in `scoop help config` for further information.

param($app)

. "$PSScriptRoot\..\lib\json.ps1" # 'ConvertToPrettyJson'
. "$PSScriptRoot\..\lib\versions.ps1" # 'Select-CurrentVersion'
. "$PSScriptRoot\..\lib\manifest.ps1" # 'Get-Manifest'
. "$PSScriptRoot\..\lib\download.ps1" # 'Get-UserAgent'

if (!$app) { error '<app> missing'; my_usage; exit 1 }

$null, $manifest, $bucket, $url = Get-Manifest $app

if ($manifest) {
    $style = get_config CAT_STYLE
    if ($style) {
        $manifest | ConvertToPrettyJson | bat --no-paging --style $style --language json
    } else {
        $manifest | ConvertToPrettyJson
    }
} else {
    abort "Couldn't find manifest for '$app'$(if($bucket) { " from '$bucket' bucket" } elseif($url) { " at '$url'" })."
}

exit $exitCode
