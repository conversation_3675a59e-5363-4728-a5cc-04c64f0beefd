<?xml version="1.0" encoding="utf-8"?>
<Configuration>
    <ViewDefinitions>
        <View>
            <Name>ScoopAppsType</Name>
            <ViewSelectedBy>
                <TypeName>ScoopApps</TypeName>
            </ViewSelectedBy>
            <TableControl>
                <TableRowEntries>
                    <TableRowEntry>
                        <TableColumnItems>
                            <TableColumnItem>
                                <PropertyName>Name</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>Version</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>Source</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>Updated</PropertyName>
                                <FormatString>yyyy-MM-dd HH:mm:ss</FormatString>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>Info</PropertyName>
                            </TableColumnItem>
                        </TableColumnItems>
                    </TableRowEntry>
                </TableRowEntries>
            </TableControl>
        </View>
        <View>
            <Name>ScoopShimsType</Name>
            <ViewSelectedBy>
                <TypeName>ScoopShims</TypeName>
            </ViewSelectedBy>
            <TableControl>
                <TableRowEntries>
                    <TableRowEntry>
                        <TableColumnItems>
                            <TableColumnItem>
                                <PropertyName>Name</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>Source</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>Alternatives</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>IsGlobal</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>IsHidden</PropertyName>
                            </TableColumnItem>
                        </TableColumnItems>
                    </TableRowEntry>
                </TableRowEntries>
            </TableControl>
        </View>
        <View>
            <Name>ScoopStatusType</Name>
            <ViewSelectedBy>
                <TypeName>ScoopStatus</TypeName>
            </ViewSelectedBy>
            <TableControl>
                <TableRowEntries>
                    <TableRowEntry>
                        <TableColumnItems>
                            <TableColumnItem>
                                <PropertyName>Name</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>Installed Version</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>Latest Version</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>Missing Dependencies</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>Info</PropertyName>
                            </TableColumnItem>
                        </TableColumnItems>
                    </TableRowEntry>
                </TableRowEntries>
            </TableControl>
        </View>
    </ViewDefinitions>
</Configuration>
