---
name: "<PERSON>ug Report"
about: "I am facing some problems."
title: '[Bug] '
labels: "bug"

---

<!--
  IMPORTANT:
  If your problem is related to a specific package, open the issue in the relevant bucket,
  not here.
  By opening this issue you confirm that you have searched for similar issues/PRs here already.
  Failing to do so will most likely result in closing of this issue without any explanation.
  Incomplete form details below might also result in closing of the issue.
-->

## Bug Report

#### Current Behavior
<!-- A clear and concise description of the behavior. -->

#### Expected Behavior
<!-- A clear and concise description of what you expected to happen. -->

#### Additional context/output
<!-- Add any other context about the problem here. If applicable, paste terminal output here to help explain. -->

#### Possible Solution
<!--- Only if you have suggestions on a fix for the bug -->

### System details

**Windows version:** [e.g. 7, 8, 10, 11]

**OS architecture:** [e.g. 32bit, 64bit, arm64]

**PowerShell version:** [output of `"$($PSVersionTable.PSVersion)"`]

**Additional software:** [(optional) e.g. <PERSON><PERSON><PERSON>, Git]

#### Scoop Configuration
<!-- Can be found in  ~/.config/scoop/config.json -->

```json
//# Your configuration here
```
