name: Scoop Core CI Tests

on:
  pull_request:
  workflow_dispatch:

jobs:
  test_powershell:
    name: WindowsPowerShell
    runs-on: windows-latest
    steps:
      - name: Checkout
        uses: actions/checkout@main
        with:
          fetch-depth: 2
      - name: Init Test Suite
        uses: potatoqualitee/psmodulecache@main
        with:
          modules-to-cache: BuildHelpers
          shell: powershell
      - name: Test Scoop Core
        shell: powershell
        run: ./test/bin/test.ps1
  test_pwsh:
    name: PowerShell
    runs-on: windows-latest
    steps:
      - name: Checkout
        uses: actions/checkout@main
        with:
          fetch-depth: 2
      - name: Init Test Suite
        uses: potatoqualitee/psmodulecache@main
        with:
          modules-to-cache: BuildHelpers
          shell: pwsh
      - name: Test Scoop Core
        shell: pwsh
        run: ./test/bin/test.ps1
