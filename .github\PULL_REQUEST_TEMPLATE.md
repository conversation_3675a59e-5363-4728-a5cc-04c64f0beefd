<!-- Provide a general summary of your changes in the Title above -->
<!-- To help with semantic versioning the PR title should start with one of the conventional commit types. -->
<!-- The conventional commit types for Semantic PR are: feat, fix, docs, style, refactor, perf, test, build, ci, chore, revert -->

<!--
  By opening this PR you confirm that you have searched for similar issues/PRs here already.
  Failing to do so will most likely result in closing of this PR without any explanation.
  It is also mandatory to open a relevant issue for discussion with the maintainers,
  before creating any new PR.
  Read the contributing guide first to save both your and our time.
-->

#### Description
<!-- Describe your changes in detail -->

#### Motivation and Context
<!-- Why is this change required? What problem does it solve? -->
<!-- If it fixes an open issue, please link to the issue here. -->
Closes #XXXX
<!-- or -->
Relates to #XXXX

#### How Has This Been Tested?
<!-- Please describe in detail how you tested your changes. -->
<!-- Include details of your testing environment, tests ran to see how -->
<!-- your change affects other areas of the code, etc. -->

#### Checklist:
<!-- Go over all the following points, and put an `x` in all the boxes that apply. -->
<!-- If you're unsure about any of these, don't hesitate to ask. We're here to help! -->
- [ ] I have read the [Contributing Guide](https://github.com/ScoopInstaller/.github/blob/main/.github/CONTRIBUTING.md).
- [ ] I have ensured that I am targeting the `develop` branch.
- [ ] I have updated the documentation accordingly.
- [ ] I have updated the tests accordingly.
- [ ] I have added an entry in the CHANGELOG.
